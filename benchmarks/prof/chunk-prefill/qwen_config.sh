#!/bin/bash
# ==================== Qwen3-30B 模型配置 ====================

# ==================== 全局配置 ====================
VLLM_PATH="/home/<USER>/"
BENCHMARK_SCRIPT="/home/<USER>/benchmarks/benchmark_serving.py"

# ==================== 服务器配置 ====================
# 服务器1 (Base任务) 完整配置
SERVER1_MODEL_NAME="qwen"
SERVER1_MODEL_PATH="/mnt/data/llm-models/qwen3/Qwen3-30B-A3B/"
SERVER1_TOKENIZER_PATH="/mnt/data/llm-models/qwen3/Qwen3-30B-A3B/"
SERVER1_PORT=8061
SERVER1_GPU_DEVICES="0,1"
SERVER1_TENSOR_PARALLEL=2
SERVER1_DTYPE="float16"
SERVER1_BLOCK_SIZE=64
SERVER1_EXTRA_ARGS=" --no-enable-prefix-caching --disable-log-requests --compilation-config '{\"level\": 3, \"use_inductor\": true, \"use_cudagraph\": false, \"full_cuda_graph\": true, \"debug_dump_path\": \"/tmp/vllm_debug_base\"}' "

# 服务器2 (Chunk任务) 完整配置
SERVER2_MODEL_NAME="qwen"
SERVER2_MODEL_PATH="/mnt/data/llm-models/qwen3/Qwen3-30B-A3B/"
SERVER2_TOKENIZER_PATH="/mnt/data/llm-models/qwen3/Qwen3-30B-A3B/"
SERVER2_PORT=8062
SERVER2_GPU_DEVICES="2,3"
SERVER2_TENSOR_PARALLEL=2
SERVER2_DTYPE="float16"
SERVER2_BLOCK_SIZE=64
SERVER2_EXTRA_ARGS="--no-enable-prefix-caching --disable-log-requests --compilation-config '{\"level\": 3, \"use_inductor\": true, \"use_cudagraph\": false, \"full_cuda_graph\": true, \"debug_dump_path\": \"/tmp/vllm_debug_chunk\"}' "

# 共同的环境变量
VLLM_USE_FLASH_ATTN_PA=1
VLLM_FLASH_ATTN_V1=1

# ==================== 备用配置 ====================
# 如果Level 3有问题，可以取消注释下面的配置并注释掉上面的Level 3配置

# Level 1配置（更稳定）
# SERVER1_EXTRA_ARGS='--no-enable-chunked-prefill --no-enable-prefix-caching --disable-log-requests --compilation-config {"level": 1, "use_cudagraph": true}'
# SERVER2_EXTRA_ARGS='--enable-chunked-prefill --no-enable-prefix-caching --disable-log-requests --compilation-config {"level": 1, "use_cudagraph": true}'

# 无编译优化配置（最稳定）
# SERVER1_EXTRA_ARGS='--no-enable-chunked-prefill --no-enable-prefix-caching --disable-log-requests'
# SERVER2_EXTRA_ARGS='--enable-chunked-prefill --no-enable-prefix-caching --disable-log-requests'

# ==================== 输出目录配置 ====================
BASE_OUTPUT_DIR="/home/<USER>/benchmarks/prof/chunk-prefill"
BASE_PROF_DIR="${BASE_OUTPUT_DIR}/qwen3-30B-base"
CHUNK_PROF_DIR="${BASE_OUTPUT_DIR}/qwen3-30B-chunk"
BASE_LOG_FILE="${BASE_PROF_DIR}/Qwen3-30B-A3B_base.log"
CHUNK_LOG_FILE="${CHUNK_PROF_DIR}/Qwen3-30B-A3B_chunk.log"
SERVER1_LOG_FILE="${BASE_OUTPUT_DIR}/Qwen3-30B-A3B_server_base.log"
SERVER2_LOG_FILE="${BASE_OUTPUT_DIR}/Qwen3-30B-A3B_server_chunk.log"

# ==================== Profiler配置 ====================
# 每个服务器独立的profiler目录，避免文件混淆
SERVER1_TORCH_PROFILER_DIR="/home/<USER>/benchmarks/prof/chunk-prefill/qwen3-30B-base/"
SERVER2_TORCH_PROFILER_DIR="/home/<USER>/benchmarks/prof/chunk-prefill/qwen3-30B-chunk/"
BASE_EXPECTED_FILES=2   # Base任务期望的profiler文件数量
CHUNK_EXPECTED_FILES=2  # Chunk任务期望的profiler文件数量

# ==================== 测试用例配置 ====================
# 格式: "input_length num_prompts"
BASE_TEST_CASES=(
    "1000 48"
    # "2000 33"
    # "3600 27"
    # "20000 8"
    # "200 67"
)

CHUNK_TEST_CASES=(
    "1000 48"
    # "2000 33"
    # "3600 27"
    # "20000 8"
    # "200 67"
)

# ==================== Benchmark参数配置 ====================
BASE_OUTPUT_LEN=10
CHUNK_OUTPUT_LEN=10
DATASET_NAME="random"
IGNORE_EOS="--ignore-eos"
PROFILE_FLAG="--profile"

# ==================== 超时和等待配置 ====================
PROFILER_TIMEOUT=180  # profiler文件生成超时时间（秒）
SLEEP_AFTER_BENCHMARK=60  # Base任务benchmark后等待时间
SLEEP_AFTER_CHUNK_BENCHMARK=60  # Chunk任务benchmark后等待时间
SLEEP_BETWEEN_CHECKS=10  # 检查文件时的间隔时间

# ==================== 锁文件配置 ====================
LOCKFILE="/tmp/prof_qwen_test.lock"
BASE_TASK_LOCK="/tmp/prof_qwen_base_task.lock"
CHUNK_TASK_LOCK="/tmp/prof_qwen_chunk_task.lock"
